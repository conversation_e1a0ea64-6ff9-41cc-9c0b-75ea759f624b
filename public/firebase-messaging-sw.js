// Minimal Firebase messaging service worker
// This file is required for Firebase messaging to work properly
// For now, we'll keep it minimal since we're focusing on foreground notifications

console.log("[firebase-messaging-sw.js] Service worker loaded");

// Handle notification click
self.addEventListener("notificationclick", (event) => {
  console.log("[firebase-messaging-sw.js] Notification click received:", event);

  event.notification.close();

  // Focus or open the app window
  event.waitUntil(
    clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then((clientList) => {
        // If a window is already open, focus it
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && "focus" in client) {
            return client.focus();
          }
        }

        // If no window is open, open a new one
        if (clients.openWindow) {
          return clients.openWindow("/");
        }
      })
  );
});

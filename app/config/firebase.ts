import { initializeApp, type FirebaseOptions } from "firebase/app";
import { getMessaging, type Messaging } from "firebase/messaging";

const firebaseConfig: FirebaseOptions = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);

// Initialize messaging in the browser with service worker
let messaging: Messaging | null = null;
let messagingPromise: Promise<Messaging | null> | null = null;

if (typeof window !== "undefined") {
  // Create a promise that resolves when messaging is ready
  messagingPromise = (async () => {
    try {
      // Register service worker first if available
      if ("serviceWorker" in navigator) {
        try {
          const registration = await navigator.serviceWorker.register(
            "/firebase-messaging-sw.js"
          );
          console.log("Service worker registered successfully:", registration);
        } catch (error) {
          console.error("Service worker registration failed:", error);
        }
      }

      // Initialize messaging
      messaging = getMessaging(app);
      console.log("Firebase messaging initialized");
      return messaging;
    } catch (error) {
      console.error("Firebase messaging initialization failed:", error);
      return null;
    }
  })();
}

// Helper function to get messaging when ready
export const getMessagingInstance = async (): Promise<Messaging | null> => {
  if (messagingPromise) {
    return await messagingPromise;
  }
  return messaging;
};

export { app, messaging };

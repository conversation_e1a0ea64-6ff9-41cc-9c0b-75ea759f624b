import { initializeApp, type FirebaseOptions } from "firebase/app";
import { getMessaging, onMessage, type Messaging } from "firebase/messaging";

const firebaseConfig: FirebaseOptions = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);

// Only initialize messaging in the browser
let messaging: Messaging | null = null;

async function registerServiceWorker(): Promise<Messaging | null> {
  try {
    // Register service worker first
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(
          "/firebase-messaging-sw.js"
        );
        console.log("Service worker registered successfully:", registration);

        return getMessaging(app);
      } catch (error) {
        console.error("Service worker registration failed:", error);
        console.log("Firebase messaging initialized without service worker");
        return getMessaging(app);
      }
    } else {
      // No service worker support, initialize messaging anyway
      console.log("Firebase messaging initialized (no service worker support)");
      return getMessaging(app);
    }
  } catch (error) {
    console.warn("Firebase messaging initialization failed:", error);
    return null;
  }
}

export { app, registerServiceWorker };

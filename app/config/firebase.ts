import { initializeApp, type FirebaseOptions } from "firebase/app";
import { getMessaging, type Messaging } from "firebase/messaging";

const firebaseConfig: FirebaseOptions = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);

// Initialize messaging in the browser with service worker
let messaging: Messaging | null = null;

if (typeof window !== "undefined") {
  // Register service worker first, then initialize messaging
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker
      .register("/firebase-messaging-sw.js")
      .then((registration) => {
        console.log("Service worker registered successfully:", registration);
        messaging = getMessaging(app);
        console.log("Firebase messaging initialized with service worker");
      })
      .catch((error) => {
        console.error("Service worker registration failed:", error);
        messaging = getMessaging(app);
        console.log("Firebase messaging initialized without service worker");
      });
  } else {
    messaging = getMessaging(app);
    console.log("Firebase messaging initialized (no service worker support)");
  }
}

export { app, messaging };

import {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useEffect,
  useState,
  type <PERSON>actNode,
} from "react";
import Session from "supertokens-web-js/recipe/session";
import type { StreamChat, Channel, Thread } from "stream-chat";
import { connect } from "getstream";

import {
  useGenerateGetStreamToken,
  useRegisterPushNotificationToken,
} from "~/lib/api/client-queries";
import { queryClient } from "~/lib/providers/query-client";
const { getMessagingInstance } = await import("~/config/firebase");

const GROUP_KEY = "current_group_id";
const COHORT_KEY = "current_cohort_id";
const FCM_TOKEN_KEY = "fcm_token";
const STREAM_API_KEY = import.meta.env.VITE_STREAM_API_KEY as
  | string
  | undefined;
const VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY as string | undefined;

if (!STREAM_API_KEY) {
  // eslint-disable-next-line no-console
  console.warn(
    "VITE_STREAM_API_KEY is not defined - Stream chat will be disabled."
  );
}

if (!VAPID_KEY) {
  // eslint-disable-next-line no-console
  console.warn(
    "VITE_FIREBASE_VAPID_KEY is not defined - Push notifications will be disabled."
  );
}

export type AppContextType = {
  chatClient: StreamChat | null;
  streamClient: any | null;
  userId: string | null;
  groupId: string | null;
  cohortId: string | null;
  streamToken: string | null;
  thread: Thread | null;
  channel: Channel | null;
  // Push notification related
  notificationPermission: NotificationPermission | null;
  fcmToken: string | null;
  isNotificationSupported: boolean;
  setUserId: (id: string | null) => void;
  setGroupId: (id: string | null) => void;
  setCohortId: (id: string | null) => void;
  setThread: (thread: Thread | null) => void;
  setChannel: (channel: Channel | null) => void;
  requestNotificationPermission: () => Promise<void>;
  cleanup: () => void;
  refreshAuth: () => Promise<void>;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [userId, setUserId] = useState<string | null>(null);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [cohortId, setCohortId] = useState<string | null>(null);
  const [thread, setThread] = useState<Thread | null>(null);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [chatClient, setChatClient] = useState<StreamChat | null>(null);
  const [streamClient, setStreamClient] = useState<any | null>(null);

  // Push notification state
  const [notificationPermission, setNotificationPermission] =
    useState<NotificationPermission | null>(null);
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [isNotificationSupported] = useState<boolean>(
    typeof window !== "undefined" &&
      "Notification" in window &&
      "serviceWorker" in navigator
  );

  const { mutate: generateToken, data: tokenData } =
    useGenerateGetStreamToken();
  const { mutate: registerPushToken } = useRegisterPushNotificationToken();

  // Convert API response shape → token string or null
  const streamToken: string | null = tokenData?.data?.token ?? null;

  // Function to request notification permission and get FCM token
  const requestNotificationPermission = async () => {
    if (!isNotificationSupported || !VAPID_KEY) {
      console.warn(
        "Push notifications are not supported or VAPID key is missing"
      );
      return;
    }

    try {
      // Request permission using Notification API
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);

      if (permission === "granted") {
        // Dynamically import Firebase messaging (client-side only)
        const { getToken } = await import("firebase/messaging");

        const messagingInstance = await getMessagingInstance();
        if (!messagingInstance) {
          console.error("Firebase messaging not available");
          return;
        }

        // Get FCM token
        const token = await getToken(messagingInstance, {
          vapidKey: VAPID_KEY,
        });

        if (token) {
          setFcmToken(token);
          // Store token in localStorage for persistence across refreshes
          localStorage.setItem(FCM_TOKEN_KEY, token);
          console.log("FCM Token generated:", token);

          // Register token with backend
          registerPushToken({
            platform: "web",
            token: token,
          });
        } else {
          console.log("No registration token available.");
        }
      } else if (permission === "denied") {
        console.log("Notification permission denied");
      } else {
        console.log("Notification permission not granted");
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
    }
  };

  // Check initial notification permission and restore FCM token on mount
  useEffect(() => {
    if (isNotificationSupported) {
      setNotificationPermission(Notification.permission);

      // Restore FCM token from localStorage if it exists
      const storedToken = localStorage.getItem(FCM_TOKEN_KEY);
      if (storedToken) {
        setFcmToken(storedToken);
        console.log("🔄 Restored FCM token from storage:", storedToken);
      }
    }
  }, [isNotificationSupported]);

  // Fetch auth status & user id via SuperTokens once on mount
  useEffect(() => {
    let mounted = true;

    Session.doesSessionExist()
      .then((exist) => {
        if (!exist || !mounted) return;
        return Session.getUserId();
      })
      .then((id) => {
        if (id && mounted) {
          setUserId(id);
          // trigger token generation
          generateToken();
        }
      })
      .catch(() => {
        // Failed to initialize auth
      });

    return () => {
      mounted = false;
    };
  }, []);

  // Request notification permission automatically when user is authenticated
  useEffect(() => {
    if (
      userId &&
      isNotificationSupported &&
      notificationPermission === "default"
    ) {
      // Automatically request permission for authenticated users
      // You can remove this if you want manual permission request only
      requestNotificationPermission();
    }
  }, [userId, isNotificationSupported, notificationPermission]);

  // Check and refresh FCM token when user is authenticated and permission is granted
  useEffect(() => {
    if (
      userId &&
      isNotificationSupported &&
      notificationPermission === "granted" &&
      !fcmToken
    ) {
      // User is authenticated and has permission but no token - get one
      console.log(
        "🔄 User authenticated with permission but no FCM token, requesting..."
      );
      requestNotificationPermission();
    }
  }, [userId, isNotificationSupported, notificationPermission, fcmToken]);

  // Set up foreground message listener when FCM token is available
  useEffect(() => {
    console.log("🔍 Message listener effect triggered:", {
      fcmToken: !!fcmToken,
      isNotificationSupported,
      fcmTokenValue: fcmToken?.substring(0, 20) + "...",
    });

    if (!fcmToken || !isNotificationSupported) {
      console.log("❌ Message listener setup skipped:", {
        fcmToken: !!fcmToken,
        isNotificationSupported,
      });
      return;
    }

    let unsubscribe: (() => void) | null = null;

    const setupMessageListener = async () => {
      try {
        console.log("🔄 Starting setupMessageListener...");
        const { onMessage } = await import("firebase/messaging");
        console.log("✅ onMessage imported successfully");

        console.log("🔄 Getting Firebase messaging instance...");
        const messagingInstance = await getMessagingInstance();
        console.log("✅ Got messaging instance:", !!messagingInstance);

        if (!messagingInstance) {
          console.error("❌ Firebase messaging not available");
          return;
        }

        console.log("🚀 Setting up foreground message listener");

        // Listen for foreground messages
        unsubscribe = onMessage(messagingInstance, (payload) => {
          console.log("🔔 Notification received in foreground:", payload);
          console.log("📧 Title:", payload.notification?.title);
          console.log("📝 Body:", payload.notification?.body);
          console.log("📊 Data:", payload.data);

          // Show browser notification
          if (payload.notification?.title && payload.notification?.body) {
            console.log(
              "🔍 Current notification permission:",
              Notification.permission
            );
            console.log("🔍 Attempting to create browser notification...");

            try {
              // Create and show browser notification
              const notification = new Notification(
                payload.notification.title,
                {
                  body: payload.notification.body,
                  icon: "/favicon.ico", // You can customize this icon
                  badge: "/favicon.ico",
                  tag: "sphere-notification", // Prevents duplicate notifications
                  requireInteraction: false, // Auto-dismiss after a few seconds
                  data: payload.data, // Store additional data
                }
              );

              console.log(
                "✅ Browser notification created successfully:",
                notification
              );

              // Handle notification click
              notification.onclick = () => {
                console.log("Notification clicked:", payload.data);

                // Focus the window when notification is clicked
                window.focus();

                // You can add navigation logic here based on payload.data
                // For example: if (payload.data?.link) { window.location.href = payload.data.link; }

                notification.close();
              };

              // Auto-close after 5 seconds (optional)
              setTimeout(() => {
                notification.close();
              }, 5000);
            } catch (error) {
              console.error("❌ Failed to create browser notification:", error);
              console.log(
                "🔍 Notification permission:",
                Notification.permission
              );
              console.log(
                "🔍 Is notification supported:",
                "Notification" in window
              );
            }
          } else {
            console.log("❌ Missing notification title or body:", {
              title: payload.notification?.title,
              body: payload.notification?.body,
            });
          }
        });

        console.log("✅ Foreground message listener set up successfully");
      } catch (error) {
        console.error("❌ Error setting up message listener:", error);
      }
    };

    setupMessageListener();

    // Cleanup listener on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
        console.log("🧹 Message listener cleaned up");
      }
    };
  }, [fcmToken, isNotificationSupported]);

  // Load persisted IDs
  useEffect(() => {
    const storedGroupId = localStorage.getItem(GROUP_KEY);
    if (storedGroupId) setGroupId(storedGroupId);
    const storedCohortId = localStorage.getItem(COHORT_KEY);
    if (storedCohortId) setCohortId(storedCohortId);
  }, []);

  // Dynamically create StreamChat client in the browser once we have auth
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    let client: StreamChat;

    (async () => {
      const { StreamChat } = await import("stream-chat");
      client = StreamChat.getInstance(STREAM_API_KEY);
      await client.connectUser({ id: userId }, streamToken);
      setChatClient(client);
    })();

    return () => {
      if (client) {
        client.disconnectUser().catch(() => {});
      }
    };
    // Only when tokens or user change
  }, [userId, streamToken]);

  // Initialize GetStream client for feeds and notifications
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    try {
      const client = connect(
        STREAM_API_KEY,
        streamToken,
        import.meta.env.VITE_STREAM_APP_ID
      );
      setStreamClient(client);
    } catch (error) {
      console.error("Failed to initialize GetStream client:", error);
    }
  }, [userId, streamToken]);

  const setGroupIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(GROUP_KEY, id);
    } else {
      localStorage.removeItem(GROUP_KEY);
    }
    setGroupId(id);
  };

  const setCohortIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(COHORT_KEY, id);
    } else {
      localStorage.removeItem(COHORT_KEY);
    }
    setCohortId(id);
  };

  const cleanup = () => {
    localStorage.removeItem(GROUP_KEY);
    localStorage.removeItem(COHORT_KEY);
    localStorage.removeItem(FCM_TOKEN_KEY);
    setGroupId(null);
    setCohortId(null);
    setUserId(null);
    setFcmToken(null);
    setNotificationPermission(null);
    queryClient.removeQueries();
  };

  const refreshAuth = async () => {
    try {
      const exists = await Session.doesSessionExist();
      if (exists) {
        const id = await Session.getUserId();
        if (id) {
          setUserId(id);
          generateToken();
        }
      }
    } catch (err) {
      console.error("Failed to refresh auth:", err);
    }
  };

  return (
    <AppContext.Provider
      value={{
        chatClient,
        streamClient,
        userId,
        groupId,
        cohortId,
        thread,
        channel,
        streamToken,
        // Push notification properties
        notificationPermission,
        fcmToken,
        isNotificationSupported,
        setUserId,
        setGroupId: setGroupIdWithStorage,
        setCohortId: setCohortIdWithStorage,
        setThread,
        setChannel,
        requestNotificationPermission,
        cleanup,
        refreshAuth,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
